// Bridge for communicating with browser extension for script operations

// Type declarations for Chrome extension API
interface ChromeRuntime {
  sendMessage: (message: ExtensionMessage, callback: (response: ExtensionResponse) => void) => void;
  lastError?: { message: string };
}

declare global {
  interface Window {
    chrome?: {
      runtime?: ChromeRuntime;
    };
  }
}

export interface ScriptData {
  code: string;
  version: number;
  updated_at: number;
}

export interface ExtensionMessage {
  name: string;
  body?: Record<string, unknown>;
}

export interface ExtensionResponse {
  success: boolean;
  data?: ScriptData | null;
  error?: string;
}

class ExtensionBridge {
  private isExtensionAvailable(): boolean {
    return typeof window !== 'undefined' && !!window.chrome && !!window.chrome.runtime;
  }

  private async sendMessage(message: ExtensionMessage): Promise<ExtensionResponse> {
    if (!this.isExtensionAvailable()) {
      throw new Error('Extension not available');
    }

    const chromeRuntime = window.chrome!.runtime!;

    return new Promise((resolve, reject) => {
      chromeRuntime.sendMessage(message, (response: ExtensionResponse) => {
        if (chromeRuntime.lastError) {
          reject(new Error(chromeRuntime.lastError.message));
          return;
        }

        if (!response) {
          reject(new Error('No response from extension'));
          return;
        }

        resolve(response);
      });
    });
  }

  async saveScript(params: {
    messageId: string;
    code: string;
    miniappId: number;
    name?: string;
    version?: number;
  }): Promise<void> {
    const response = await this.sendMessage({
      name: 'save-script',
      body: params,
    });

    if (!response.success) {
      throw new Error(response.error || 'Failed to save script');
    }
  }

  async getScript(miniappId: number): Promise<ScriptData | null> {
    const response = await this.sendMessage({
      name: 'get-script',
      body: { miniappId },
    });

    if (!response.success) {
      throw new Error(response.error || 'Failed to get script');
    }

    return response.data || null;
  }

  async deleteScript(miniappId: number): Promise<void> {
    const response = await this.sendMessage({
      name: 'delete-script',
      body: { miniappId },
    });

    if (!response.success) {
      throw new Error(response.error || 'Failed to delete script');
    }
  }
}

export const extensionBridge = new ExtensionBridge();
