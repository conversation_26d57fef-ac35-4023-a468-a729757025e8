// Bridge for communicating with browser extension for script operations

export interface ScriptData {
  code: string;
  version: number;
  updated_at: number;
}

export interface ExtensionMessage {
  type: string;
  action: string;
  data?: Record<string, unknown>;
  requestId?: string;
}

export interface ExtensionResponse {
  success: boolean;
  data?: ScriptData | null;
  error?: string;
  requestId?: string;
}

class ExtensionBridge {
  private pendingRequests = new Map<
    string,
    {
      resolve: (value: ExtensionResponse) => void;
      reject: (error: Error) => void;
      timeout: NodeJS.Timeout;
    }
  >();

  constructor() {
    // Listen for messages from extension
    if (typeof window !== 'undefined') {
      window.addEventListener('message', this.handleMessage.bind(this));
    }
  }

  private handleMessage(event: MessageEvent) {
    // Only accept messages from same origin for security
    if (event.origin !== window.location.origin) {
      return;
    }

    const message = event.data;
    if (message?.type === 'FROM_EXTENSION_TO_WEB' && message.requestId) {
      const pending = this.pendingRequests.get(message.requestId);
      if (pending) {
        clearTimeout(pending.timeout);
        this.pendingRequests.delete(message.requestId);

        if (message.success !== false) {
          pending.resolve(message);
        } else {
          pending.reject(new Error(message.error || 'Extension request failed'));
        }
      }
    }
  }

  private async sendMessage(
    action: string,
    data?: Record<string, unknown>
  ): Promise<ExtensionResponse> {
    return new Promise((resolve, reject) => {
      const requestId = `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

      // Set up timeout
      const timeout = setTimeout(() => {
        this.pendingRequests.delete(requestId);
        reject(new Error('Extension request timeout'));
      }, 5000); // 5 second timeout

      // Store pending request
      this.pendingRequests.set(requestId, { resolve, reject, timeout });

      // Send message to extension via window.postMessage
      const message: ExtensionMessage = {
        type: 'FROM_WEB_TO_EXTENSION',
        action,
        data,
        requestId,
      };

      window.postMessage(message, '*');
    });
  }

  async saveScript(params: {
    messageId: string;
    code: string;
    miniappId: number;
    name?: string;
    version?: number;
  }): Promise<void> {
    const response = await this.sendMessage('save-script', params);

    if (!response.success) {
      throw new Error(response.error || 'Failed to save script');
    }
  }

  async getScript(miniappId: number): Promise<ScriptData | null> {
    const response = await this.sendMessage('get-script', { miniappId });

    if (!response.success) {
      throw new Error(response.error || 'Failed to get script');
    }

    return response.data || null;
  }

  async deleteScript(miniappId: number): Promise<void> {
    const response = await this.sendMessage('delete-script', { miniappId });

    if (!response.success) {
      throw new Error(response.error || 'Failed to delete script');
    }
  }

  // Cleanup method
  destroy() {
    // Clear all pending requests
    this.pendingRequests.forEach(({ timeout, reject }) => {
      clearTimeout(timeout);
      reject(new Error('Extension bridge destroyed'));
    });
    this.pendingRequests.clear();

    // Remove event listener
    if (typeof window !== 'undefined') {
      window.removeEventListener('message', this.handleMessage.bind(this));
    }
  }
}

export const extensionBridge = new ExtensionBridge();
