// Example usage of the extension bridge

import { extensionBridge } from './extension-bridge';

// Example: Save a script to extension storage
async function saveMyScript() {
  try {
    await extensionBridge.saveScript({
      messageId: 'msg-123',
      code: 'console.log("Hello World!");',
      miniappId: 1,
      name: 'My Script',
      version: 1,
    });
    console.log('✅ Script saved successfully!');
  } catch (error) {
    console.error('❌ Failed to save script:', error);
  }
}

// Example: Load a script from extension storage
async function loadMyScript() {
  try {
    const script = await extensionBridge.getScript(1);
    if (script) {
      console.log('📜 Script loaded:', script.code);
      console.log('🔢 Version:', script.version);
    } else {
      console.log('📭 No script found');
    }
  } catch (error) {
    console.error('❌ Failed to load script:', error);
  }
}

// Example: Delete a script from extension storage
async function deleteMyScript() {
  try {
    await extensionBridge.deleteScript(1);
    console.log('🗑️ Script deleted successfully!');
  } catch (error) {
    console.error('❌ Failed to delete script:', error);
  }
}
