import { extensionBridge } from './extension-bridge';

// Script interface for compatibility
export interface Script {
  message_id: string;
  displayCode: string;
  updated_at: number;
  miniapp_id?: string;
  name?: string;
  version?: number;
}

// Synchronization events
export interface ScriptSyncEvent {
  type: 'script-updated' | 'script-deleted';
  messageId: string;
  script?: Script;
  timestamp: number;
}

class ScriptSyncManager {
  private channel: BroadcastChannel | null = null;
  private listeners: Set<(event: ScriptSyncEvent) => void> = new Set();

  constructor() {
    // Try to create BroadcastChannel for cross-context communication
    try {
      this.channel = new BroadcastChannel('script-sync');
      this.channel.addEventListener('message', this.handleBroadcastMessage.bind(this));
    } catch (error) {
      console.warn('BroadcastChannel not available, using fallback sync', error);
    }
  }

  private handleBroadcastMessage(event: MessageEvent<ScriptSyncEvent>) {
    this.notifyListeners(event.data);
  }

  private notifyListeners(event: ScriptSyncEvent) {
    this.listeners.forEach(listener => {
      try {
        listener(event);
      } catch (error) {
        console.error('Error in sync listener:', error);
      }
    });
  }

  // Save script and notify other contexts
  async saveScript(script: Script): Promise<void> {
    if (!script.miniapp_id) {
      throw new Error('miniapp_id is required for script operations');
    }

    script.updated_at = Date.now();

    try {
      // Check if extension is available first
      const isExtensionAvailable = await extensionBridge.isExtensionAvailable();

      if (isExtensionAvailable) {
        await extensionBridge.saveScript({
          messageId: script.message_id,
          code: script.displayCode,
          miniappId: parseInt(script.miniapp_id),
          name: script.name,
          version: script.version || 1,
        });
      } else {
        console.warn('Extension not available, script saved locally only');
        // Store in localStorage as fallback
        this.saveToLocalStorage(script);
      }

      const event: ScriptSyncEvent = {
        type: 'script-updated',
        messageId: script.message_id,
        script,
        timestamp: Date.now(),
      };

      this.broadcastEvent(event);
    } catch (error) {
      console.error('Failed to save script via extension:', error);
      // Fallback to localStorage
      this.saveToLocalStorage(script);

      const event: ScriptSyncEvent = {
        type: 'script-updated',
        messageId: script.message_id,
        script,
        timestamp: Date.now(),
      };

      this.broadcastEvent(event);
    }
  }

  // Delete script and notify other contexts
  async deleteScript(messageId: string, miniappId?: string): Promise<void> {
    try {
      // Check if extension is available first
      const isExtensionAvailable = await extensionBridge.isExtensionAvailable();

      if (isExtensionAvailable && miniappId) {
        await extensionBridge.deleteScript(parseInt(miniappId));
      } else {
        console.warn(
          'Extension not available or miniappId missing, removing from localStorage only'
        );
      }

      // Always remove from localStorage as well
      this.removeFromLocalStorage(messageId);

      const event: ScriptSyncEvent = {
        type: 'script-deleted',
        messageId,
        timestamp: Date.now(),
      };

      this.broadcastEvent(event);
    } catch (error) {
      console.error('Failed to delete script via extension:', error);
      // Still remove from localStorage
      this.removeFromLocalStorage(messageId);

      const event: ScriptSyncEvent = {
        type: 'script-deleted',
        messageId,
        timestamp: Date.now(),
      };

      this.broadcastEvent(event);
    }
  }

  // Get script from extension storage with localStorage fallback
  async getScript(messageId: string, miniappId?: string): Promise<Script | null> {
    if (!miniappId) {
      console.warn('miniappId not provided, trying localStorage fallback');
      return this.getFromLocalStorage(messageId);
    }

    try {
      // Check if extension is available first
      const isExtensionAvailable = await extensionBridge.isExtensionAvailable();

      if (isExtensionAvailable) {
        const scriptData = await extensionBridge.getScript(parseInt(miniappId));

        if (scriptData) {
          return {
            message_id: messageId,
            displayCode: scriptData.code,
            updated_at: scriptData.updated_at,
            miniapp_id: miniappId,
            version: scriptData.version,
          };
        }
      }

      // Fallback to localStorage
      return this.getFromLocalStorage(messageId);
    } catch (error) {
      console.error('Failed to get script via extension:', error);
      // Fallback to localStorage
      return this.getFromLocalStorage(messageId);
    }
  }

  // Get script from miniapp developing field (new method)
  async getScriptFromMiniapp(miniappId: number): Promise<Script | null> {
    try {
      const scriptData = await extensionBridge.getScript(miniappId);

      if (!scriptData) {
        return null;
      }

      return {
        message_id: `miniapp-${miniappId}`,
        displayCode: scriptData.code,
        updated_at: scriptData.updated_at,
        miniapp_id: miniappId.toString(),
        version: scriptData.version,
      };
    } catch (error) {
      console.error('Failed to get script from miniapp:', error);
      return null;
    }
  }

  private broadcastEvent(event: ScriptSyncEvent) {
    if (this.channel) {
      try {
        this.channel.postMessage(event);
      } catch (error) {
        console.warn('Failed to broadcast sync event:', error);
      }
    }
  }

  // Listen for sync events from other contexts
  addSyncListener(listener: (event: ScriptSyncEvent) => void) {
    this.listeners.add(listener);
    return () => {
      this.listeners.delete(listener);
    };
  }

  // LocalStorage fallback methods
  private saveToLocalStorage(script: Script): void {
    try {
      const key = `script_${script.message_id}`;
      localStorage.setItem(key, JSON.stringify(script));
    } catch (error) {
      console.error('Failed to save script to localStorage:', error);
    }
  }

  private getFromLocalStorage(messageId: string): Script | null {
    try {
      const key = `script_${messageId}`;
      const stored = localStorage.getItem(key);
      if (stored) {
        return JSON.parse(stored) as Script;
      }
    } catch (error) {
      console.error('Failed to get script from localStorage:', error);
    }
    return null;
  }

  private removeFromLocalStorage(messageId: string): void {
    try {
      const key = `script_${messageId}`;
      localStorage.removeItem(key);
    } catch (error) {
      console.error('Failed to remove script from localStorage:', error);
    }
  }

  // Cleanup
  destroy() {
    if (this.channel) {
      this.channel.close();
    }
    this.listeners.clear();
  }
}

// Singleton instance
export const scriptSyncManager = new ScriptSyncManager();
