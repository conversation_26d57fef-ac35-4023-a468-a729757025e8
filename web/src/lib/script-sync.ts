import { extensionBridge } from './extension-bridge';

// Script interface for compatibility
export interface Script {
  message_id: string;
  displayCode: string;
  updated_at: number;
  miniapp_id?: string;
  name?: string;
  version?: number;
}

// Synchronization events
export interface ScriptSyncEvent {
  type: 'script-updated' | 'script-deleted';
  messageId: string;
  script?: Script;
  timestamp: number;
}

class ScriptSyncManager {
  private channel: BroadcastChannel | null = null;
  private listeners: Set<(event: ScriptSyncEvent) => void> = new Set();

  constructor() {
    // Try to create BroadcastChannel for cross-context communication
    try {
      this.channel = new BroadcastChannel('script-sync');
      this.channel.addEventListener('message', this.handleBroadcastMessage.bind(this));
    } catch (error) {
      console.warn('BroadcastChannel not available, using fallback sync');
    }
  }

  private handleBroadcastMessage(event: MessageEvent<ScriptSyncEvent>) {
    this.notifyListeners(event.data);
  }

  private notifyListeners(event: ScriptSyncEvent) {
    this.listeners.forEach(listener => {
      try {
        listener(event);
      } catch (error) {
        console.error('Error in sync listener:', error);
      }
    });
  }

  // Save script and notify other contexts
  async saveScript(script: Script): Promise<void> {
    if (!script.miniapp_id) {
      throw new Error('miniapp_id is required for script operations');
    }

    script.updated_at = Date.now();

    try {
      await extensionBridge.saveScript({
        messageId: script.message_id,
        code: script.displayCode,
        miniappId: parseInt(script.miniapp_id),
        name: script.name,
        version: script.version || 1,
      });

      const event: ScriptSyncEvent = {
        type: 'script-updated',
        messageId: script.message_id,
        script,
        timestamp: Date.now(),
      };

      this.broadcastEvent(event);
    } catch (error) {
      console.error('Failed to save script via extension:', error);
      throw error;
    }
  }

  // Delete script and notify other contexts
  async deleteScript(messageId: string, miniappId?: string): Promise<void> {
    if (!miniappId) {
      throw new Error('miniappId is required for script deletion');
    }

    try {
      await extensionBridge.deleteScript(parseInt(miniappId));

      const event: ScriptSyncEvent = {
        type: 'script-deleted',
        messageId,
        timestamp: Date.now(),
      };

      this.broadcastEvent(event);
    } catch (error) {
      console.error('Failed to delete script via extension:', error);
      throw error;
    }
  }

  // Get script from extension storage
  async getScript(messageId: string, miniappId?: string): Promise<Script | null> {
    if (!miniappId) {
      console.warn('miniappId not provided, cannot fetch script from extension');
      return null;
    }

    try {
      const scriptData = await extensionBridge.getScript(parseInt(miniappId));

      if (!scriptData) {
        return null;
      }

      return {
        message_id: messageId,
        displayCode: scriptData.code,
        updated_at: scriptData.updated_at,
        miniapp_id: miniappId,
        version: scriptData.version,
      };
    } catch (error) {
      console.error('Failed to get script via extension:', error);
      return null;
    }
  }

  // Get script from miniapp developing field (new method)
  async getScriptFromMiniapp(miniappId: number): Promise<Script | null> {
    try {
      const scriptData = await extensionBridge.getScript(miniappId);

      if (!scriptData) {
        return null;
      }

      return {
        message_id: `miniapp-${miniappId}`,
        displayCode: scriptData.code,
        updated_at: scriptData.updated_at,
        miniapp_id: miniappId.toString(),
        version: scriptData.version,
      };
    } catch (error) {
      console.error('Failed to get script from miniapp:', error);
      return null;
    }
  }

  private broadcastEvent(event: ScriptSyncEvent) {
    if (this.channel) {
      try {
        this.channel.postMessage(event);
      } catch (error) {
        console.warn('Failed to broadcast sync event:', error);
      }
    }
  }

  // Listen for sync events from other contexts
  addSyncListener(listener: (event: ScriptSyncEvent) => void) {
    this.listeners.add(listener);
    return () => {
      this.listeners.delete(listener);
    };
  }

  // Cleanup
  destroy() {
    if (this.channel) {
      this.channel.close();
    }
    this.listeners.clear();
  }
}

// Singleton instance
export const scriptSyncManager = new ScriptSyncManager();
