'use client';

import { useParams, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import Image from 'next/image';
import { scriptSyncManager } from '@/lib/script-sync';

export default function ScriptViewPage() {
  const params = useParams();
  const searchParams = useSearchParams();
  const [code, setCode] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [isEditing, setIsEditing] = useState(false);
  const [editedCode, setEditedCode] = useState<string>('');

  // Mock data from URL parameters
  const [mockData, setMockData] = useState({
    name: 'Web top-pinning plugin',
    version: 3,
    miniapp_id: 'web-top-pinning-plugin',
    message_id: 'msg-12345',
  });

  const miniapp_id = params.miniapp_id as string;
  const message_id = params.message_id as string;

  useEffect(() => {
    const loadData = async () => {
      try {
        // Load mockData from URL parameters
        const name = searchParams.get('name') || mockData.name;
        const version = parseInt(searchParams.get('version') || '3');
        const miniappId = searchParams.get('miniapp_id') || miniapp_id;
        const messageId = searchParams.get('message_id') || message_id;

        setMockData({
          name,
          version,
          miniapp_id: miniappId,
          message_id: messageId,
        });

        // Always load the latest script from extension storage first
        console.log('Loading latest script for messageId:', messageId, 'miniappId:', miniappId);
        const script = await scriptSyncManager.getScript(messageId, miniappId);
        console.log('Script from extension storage:', script);

        if (script && script.displayCode) {
          console.log('Using latest script from extension storage');
          setCode(script.displayCode);
          setEditedCode(script.displayCode);
        } else {
          console.log('Script not found in extension storage, trying URL fallbacks');
          // Fallback: try to get code from URL parameters
          let codeFound = false;
          let fallbackCode = '';

          // Try base64 encoded code first
          const code64Param = searchParams.get('code64');
          if (code64Param) {
            try {
              fallbackCode = atob(code64Param);
              codeFound = true;
            } catch (error) {
              console.warn('Failed to decode base64 code:', error);
            }
          }

          // Try regular encoded code as secondary fallback
          if (!codeFound) {
            const codeParam = searchParams.get('code');
            if (codeParam) {
              try {
                fallbackCode = decodeURIComponent(codeParam);
                codeFound = true;
              } catch (error) {
                console.warn('Failed to decode URL code:', error);
              }
            }
          }

          if (codeFound) {
            setCode(fallbackCode);
            setEditedCode(fallbackCode);

            // Store in extension storage for future use
            await scriptSyncManager.saveScript({
              message_id: messageId,
              displayCode: fallbackCode,
              updated_at: Date.now(),
              miniapp_id: miniappId,
              name,
              version,
            });
          } else {
            setCode('Error: No code found');
            setEditedCode('Error: No code found');
          }
        }
      } catch (error) {
        console.error('Failed to load script data:', error);
        setCode('Error: Failed to load script');
        setEditedCode('Error: Failed to load script');
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [searchParams, miniapp_id, message_id, mockData.name]);

  // Listen for sync events from other contexts (extension)
  useEffect(() => {
    const unsubscribe = scriptSyncManager.addSyncListener(event => {
      if (
        event.messageId === mockData.message_id &&
        event.type === 'script-updated' &&
        event.script
      ) {
        setCode(event.script.displayCode);
        setEditedCode(event.script.displayCode);
      }
    });

    return unsubscribe;
  }, [mockData.message_id]);

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleSave = async () => {
    try {
      // Update local state first
      setCode(editedCode);
      setIsEditing(false);

      // Save to extension storage with latest timestamp - this will trigger sync events
      const updatedScript = {
        message_id: mockData.message_id,
        displayCode: editedCode,
        updated_at: Date.now(),
        miniapp_id: mockData.miniapp_id,
        name: mockData.name,
        version: mockData.version,
      };

      await scriptSyncManager.saveScript(updatedScript);
      console.log('Script saved successfully to extension storage:', updatedScript);
    } catch (error) {
      console.error('Failed to save script:', error);
      // Revert state on error
      setCode(code);
      setEditedCode(code);
      setIsEditing(true);
    }
  };

  const handleRun = () => {
    // Implement run functionality
    console.log('Running code:', code);
  };

  const handleCopy = () => {
    navigator.clipboard.writeText(code);
  };

  const handleClose = () => {
    window.close();
  };

  if (loading) {
    return (
      <div
        style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
          fontSize: '18px',
          color: '#6b7280',
        }}
      >
        Loading...
      </div>
    );
  }

  return (
    <div
      style={{
        height: '100vh',
        display: 'flex',
        flexDirection: 'column',
        backgroundColor: '#f0f0f0',
        fontFamily: 'Arial, sans-serif',
      }}
    >
      {/* Fixed Top Header */}
      <div
        style={{
          backgroundColor: '#fff',
          height: '64px', // h-16 equivalent
          padding: '0 20px',
          boxShadow: '0 2px 5px rgba(0, 0, 0, 0.1)',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          borderBottom: '1px solid #ddd',
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          zIndex: 1000,
        }}
      >
        {/* Left side - Name and Version */}
        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
          <span
            style={{
              fontSize: '18px',
              fontWeight: 'bold',
              color: '#333',
            }}
          >
            {mockData.name}
          </span>
          <span
            style={{
              fontSize: '14px',
              color: '#666',
              backgroundColor: '#f0f0f0',
              padding: '4px 8px',
              borderRadius: '6px',
            }}
          >
            Version {mockData.version}
          </span>
        </div>

        {/* Right side - Action Buttons */}
        <div style={{ display: 'flex', gap: '8px' }}>
          <button
            onClick={handleEdit}
            style={{
              width: '40px',
              height: '40px',
              border: 'none',
              borderRadius: '8px',
              backgroundColor: '#f8f9fa',
              cursor: 'pointer',
              transition: 'background-color 0.2s',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
            onMouseEnter={e => {
              e.currentTarget.style.backgroundColor = '#e9ecef';
            }}
            onMouseLeave={e => {
              e.currentTarget.style.backgroundColor = '#f8f9fa';
            }}
          >
            <Image src="/edit.svg" alt="Edit" width={20} height={20} />
          </button>
          <button
            onClick={handleRun}
            style={{
              width: '40px',
              height: '40px',
              border: 'none',
              borderRadius: '8px',
              backgroundColor: '#f8f9fa',
              cursor: 'pointer',
              transition: 'background-color 0.2s',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
            onMouseEnter={e => {
              e.currentTarget.style.backgroundColor = '#e9ecef';
            }}
            onMouseLeave={e => {
              e.currentTarget.style.backgroundColor = '#f8f9fa';
            }}
          >
            <Image src="/run.svg" alt="Run" width={20} height={20} />
          </button>
          <button
            onClick={handleCopy}
            style={{
              width: '40px',
              height: '40px',
              border: 'none',
              borderRadius: '8px',
              backgroundColor: '#f8f9fa',
              cursor: 'pointer',
              transition: 'background-color 0.2s',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
            onMouseEnter={e => {
              e.currentTarget.style.backgroundColor = '#e9ecef';
            }}
            onMouseLeave={e => {
              e.currentTarget.style.backgroundColor = '#f8f9fa';
            }}
          >
            <Image src="/copy.svg" alt="Copy" width={20} height={20} />
          </button>
          <button
            onClick={handleClose}
            style={{
              width: '40px',
              height: '40px',
              border: 'none',
              borderRadius: '8px',
              backgroundColor: '#f8f9fa',
              cursor: 'pointer',
              transition: 'background-color 0.2s',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
            onMouseEnter={e => {
              e.currentTarget.style.backgroundColor = '#e9ecef';
            }}
            onMouseLeave={e => {
              e.currentTarget.style.backgroundColor = '#f8f9fa';
            }}
          >
            <Image src="/close.svg" alt="Close" width={20} height={20} />
          </button>
        </div>
      </div>

      {/* Scrollable Code Content */}
      <div
        style={{
          flex: 1,
          marginTop: '64px', // Account for fixed header (h-16)
          display: 'flex',
          backgroundColor: '#fff',
        }}
      >
        {/* Line Numbers */}
        <div
          style={{
            width: '60px',
            backgroundColor: '#f5f5f5',
            borderRight: '1px solid #ddd',
            padding: '10px 5px',
            fontSize: '14px',
            color: '#666',
            fontFamily: 'Monaco, Menlo, Ubuntu Mono, monospace',
            lineHeight: '1.5',
            textAlign: 'right',
            overflow: 'hidden',
          }}
        >
          {(isEditing ? editedCode : code).split('\n').map((_, index) => (
            <div key={index} style={{ height: '21px' }}>
              {index + 1}
            </div>
          ))}
        </div>

        {/* Code Editor/Viewer */}
        <div style={{ flex: 1, position: 'relative' }}>
          {isEditing ? (
            <textarea
              value={editedCode}
              onChange={e => setEditedCode(e.target.value)}
              style={{
                width: '100%',
                height: '100%',
                border: 'none',
                outline: 'none',
                padding: '10px',
                fontSize: '14px',
                fontFamily: 'Monaco, Menlo, Ubuntu Mono, monospace',
                lineHeight: '1.5',
                resize: 'none',
                backgroundColor: '#fff',
              }}
            />
          ) : (
            <pre
              style={{
                margin: 0,
                padding: '10px',
                fontSize: '14px',
                fontFamily: 'Monaco, Menlo, Ubuntu Mono, monospace',
                lineHeight: '1.5',
                color: '#333',
                whiteSpace: 'pre-wrap',
                wordBreak: 'break-word',
                backgroundColor: '#fff',
                overflow: 'auto',
                height: '100%',
              }}
            >
              {code}
            </pre>
          )}

          {/* Save Button (only visible when editing) */}
          {isEditing && (
            <button
              onClick={handleSave}
              style={{
                position: 'absolute',
                top: '10px',
                right: '10px',
                padding: '8px 16px',
                border: 'none',
                borderRadius: '4px',
                backgroundColor: '#28a745',
                color: 'white',
                fontSize: '14px',
                cursor: 'pointer',
                boxShadow: '0 2px 4px rgba(0, 0, 0, 0.2)',
              }}
            >
              💾 Save
            </button>
          )}
        </div>
      </div>
    </div>
  );
}
