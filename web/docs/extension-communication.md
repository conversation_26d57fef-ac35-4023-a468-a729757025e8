# Extension Communication Protocol

This document describes the message-based communication protocol between the web application and browser extension.

## Message Format

### From Web to Extension
```javascript
{
  type: 'FROM_WEB_TO_EXTENSION',
  action: string,
  data?: Record<string, unknown>,
  requestId?: string
}
```

### From Extension to Web
```javascript
{
  type: 'FROM_EXTENSION_TO_WEB',
  success: boolean,
  data?: ScriptData | null,
  error?: string,
  requestId?: string
}
```

## Supported Actions

### 1. ping
Check if extension is available.

**Request:**
```javascript
{
  type: 'FROM_WEB_TO_EXTENSION',
  action: 'ping',
  requestId: 'req_123'
}
```

**Response:**
```javascript
{
  type: 'FROM_EXTENSION_TO_WEB',
  success: true,
  requestId: 'req_123'
}
```

### 2. save-script
Save script to extension storage.

**Request:**
```javascript
{
  type: 'FROM_WEB_TO_EXTENSION',
  action: 'save-script',
  data: {
    messageId: string,
    code: string,
    miniappId: number,
    name?: string,
    version?: number
  },
  requestId: 'req_123'
}
```

**Response:**
```javascript
{
  type: 'FROM_EXTENSION_TO_WEB',
  success: true,
  requestId: 'req_123'
}
```

### 3. get-script
Retrieve script from extension storage.

**Request:**
```javascript
{
  type: 'FROM_WEB_TO_EXTENSION',
  action: 'get-script',
  data: {
    miniappId: number
  },
  requestId: 'req_123'
}
```

**Response:**
```javascript
{
  type: 'FROM_EXTENSION_TO_WEB',
  success: true,
  data: {
    code: string,
    version: number,
    updated_at: number
  },
  requestId: 'req_123'
}
```

### 4. delete-script
Delete script from extension storage.

**Request:**
```javascript
{
  type: 'FROM_WEB_TO_EXTENSION',
  action: 'delete-script',
  data: {
    miniappId: number
  },
  requestId: 'req_123'
}
```

**Response:**
```javascript
{
  type: 'FROM_EXTENSION_TO_WEB',
  success: true,
  requestId: 'req_123'
}
```

## Extension Implementation Example

```javascript
// In your extension's content script or background script
window.addEventListener('message', async (event) => {
  // Only accept messages from same origin
  if (event.origin !== window.location.origin) {
    return;
  }

  const message = event.data;
  if (message?.type === 'FROM_WEB_TO_EXTENSION') {
    try {
      let response = {
        type: 'FROM_EXTENSION_TO_WEB',
        success: true,
        requestId: message.requestId
      };

      switch (message.action) {
        case 'ping':
          // Just respond with success
          break;

        case 'save-script':
          // Save to extension storage (e.g., chrome.storage.local)
          await saveScriptToStorage(message.data);
          break;

        case 'get-script':
          // Retrieve from extension storage
          const scriptData = await getScriptFromStorage(message.data.miniappId);
          response.data = scriptData;
          break;

        case 'delete-script':
          // Delete from extension storage
          await deleteScriptFromStorage(message.data.miniappId);
          break;

        default:
          response.success = false;
          response.error = `Unknown action: ${message.action}`;
      }

      // Send response back to web page
      window.postMessage(response, '*');
    } catch (error) {
      // Send error response
      window.postMessage({
        type: 'FROM_EXTENSION_TO_WEB',
        success: false,
        error: error.message,
        requestId: message.requestId
      }, '*');
    }
  }
});
```

## Fallback Behavior

When the extension is not available:
1. Scripts are saved to and retrieved from localStorage
2. Cross-tab synchronization still works via BroadcastChannel
3. No errors are thrown - the system gracefully degrades

## Security Considerations

1. Messages are only accepted from the same origin
2. Request IDs prevent message replay attacks
3. Timeouts prevent hanging requests
4. All data is validated before processing
