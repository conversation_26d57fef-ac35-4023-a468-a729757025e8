import { ToolDescription } from '~/types/tools';

export const TAB_TOOLKIT_TOOLS: ToolDescription[] = [
  {
    name: 'TabToolkit_openTab',
    description:
      "Open a new browser tab with the specified URL. If a tab with the exact same URL is already open, do not open a new tab—instead, return the existing tab's ID and indicate that it was already open. Use this tool to help the user navigate to a specific website or web application.",
    parameters: {
      type: 'object',
      properties: {
        url: {
          type: 'string',
          description:
            "The full URL to open in a new browser tab. Must be a valid and complete URL (e.g., 'https://twitter.com').",
        },
      },
      required: ['url'],
    },
    returns: {
      type: 'object',
      description: 'Information about the tab that was opened or reused.',
      properties: {
        tabId: {
          type: 'number',
          description: 'The unique ID of the tab that was opened or reused.',
        },
        alreadyOpened: {
          type: 'boolean',
          description: 'True if the tab was already open, false if a new tab was created.',
        },
        success: {
          type: 'boolean',
          description: 'True if the operation succeeded, false otherwise.',
        },
        error: {
          type: 'string',
          description: 'Error message if the operation failed.',
        },
      },
    },
  },
  {
    name: 'TabToolkit_closeTab',
    description: 'Close a specific tab by its ID',
    parameters: {
      type: 'object',
      properties: {
        tabId: {
          type: 'number',
          description: 'The ID of the tab to close',
        },
      },
      required: ['tabId'],
    },
    returns: {
      type: 'object',
      description: 'Result of the close operation',
      properties: {
        success: {
          type: 'boolean',
          description: 'Whether the tab was successfully closed',
        },
      },
    },
  },
  {
    name: 'TabToolkit_listTabs',
    description: 'List all tabs in the current window',
    returns: {
      type: 'array',
      description: 'List of matching tabs',
      properties: {
        items: {
          type: 'object',
          description: 'Tab information',
          properties: {
            tabId: {
              type: 'number',
              description: 'tab ID',
            },
            url: {
              type: 'string',
              description: 'tab url',
            },
            title: {
              type: 'string',
              description: 'tab title',
            },
          },
        },
      },
    },
  },
  {
    name: 'TabToolkit_switchToTab',
    description: 'Switch to a specific tab by its ID',
    parameters: {
      type: 'object',
      properties: {
        tabId: {
          type: 'number',
          description: 'The ID of the tab to switch to',
        },
      },
      required: ['tabId'],
    },
    returns: {
      type: 'object',
      description: 'Result of the switch operation',
      properties: {
        success: {
          type: 'boolean',
          description: 'Whether the switch was successful',
        },
      },
    },
  },
  {
    name: 'TabToolkit_getCurrentActiveTab',
    description: 'Get the currently active tab in the current window',
    parameters: {
      type: 'object',
      properties: {},
    },
    returns: {
      type: 'object',
      description: 'Information about the active tab',
      properties: {
        tabId: {
          type: 'number',
          description: 'The ID of the active tab',
        },
        url: {
          type: 'string',
          description: 'The URL of the active tab',
        },
        title: {
          type: 'string',
          description: 'The title of the active tab',
        },
      },
    },
  },
];

export const WEB_TOOLKIT_ACTION_TOOLS: ToolDescription[] = [
  {
    name: 'WebToolkit_input',
    description: 'Types text into an input element using its selector or index.',
    parameters: {
      type: 'object',
      properties: {
        selectorOrIndex: {
          type: 'string',
          description:
            'Element identifier - can be either:\n1. Index number (e.g., "0", "1", "2") from the buildDomTree result - use this when you want to click elements from the analyzed DOM tree\n2. CSS selector (e.g., "#submit-button", ".login-form input[type=text]") - use this when you know the page structure and want to target specific elements directly\n\nNote: If a specific selector fails, try using "analyzePageDOM" first to find the correct selector, or use common fallbacks like "textarea", "[contenteditable=true]", or "[role=textbox]"',
        },
        value: {
          type: 'string',
          description: 'The text to input into the element',
        },
        options: {
          type: 'object',
          description: 'Optional configuration for input behavior',
          properties: {
            clearFirst: {
              type: 'boolean',
              description: 'If true, clears existing content before inputting. Default: true',
            },
            delay: {
              type: 'number',
              description: 'Delay (in milliseconds) between keystrokes. Default: 100',
            },
            pressEnterAfterInput: {
              type: 'boolean',
              description:
                'If true, simulates pressing the Enter key after inputting the value. Useful for submitting search forms or triggering actions. Default: false',
            },
          },
        },
      },
      required: ['selectorOrIndex', 'value'],
    },
    returns: {
      type: 'object',
      description: 'The result of the input action',
      properties: {
        success: {
          type: 'boolean',
          description: 'Indicates whether the text was successfully input',
        },
        error: {
          type: 'string',
          description: 'Error message if the input failed',
        },
        data: {
          type: 'object',
          description: 'Additional information about the input operation',
          properties: {
            text: {
              type: 'string',
              description: 'Text content of the element',
            },
            value: {
              type: 'string',
              description: 'Value of the input element',
            },
            html: {
              type: 'string',
              description: 'HTML content of the element',
            },
          },
        },
      },
    },
  },
  {
    name: 'WebToolkit_scrollTo',
    description: 'Scroll the page to bring an element into view using its selector or index.',
    parameters: {
      type: 'object',
      properties: {
        selectorOrIndex: {
          type: 'string',
          description:
            'Element identifier - can be either:\n1. Index number (e.g., "0", "1", "2") from the buildDomTree result - use this when you want to click elements from the analyzed DOM tree\n2. CSS selector (e.g., "#submit-button", ".login-form input[type=text]") - use this when you know the page structure and want to target specific elements directly',
        },
      },
      required: ['selectorOrIndex'],
    },
    returns: {
      type: 'object',
      description: 'Result of the scroll operation',
      properties: {
        success: {
          type: 'boolean',
          description: 'Whether the page was successfully scrolled',
        },
        error: {
          type: 'string',
          description: 'Error message if scroll failed',
        },
        data: {
          type: 'object',
          description: 'Scroll operation details',
          properties: {
            scrolled: {
              type: 'boolean',
              description: 'Whether the scroll action was performed',
            },
            elementFound: {
              type: 'boolean',
              description: 'Whether the element was found in analysis data',
            },
            elementStillExists: {
              type: 'boolean',
              description: 'Whether the element still exists on the page',
            },
          },
        },
      },
    },
  },
  {
    name: 'WebToolkit_refreshPage',
    description:
      "Refresh the current page based on the user's context. This will reload the current page and wait for it to be fully loaded. The page to refresh is determined by the user's current context and cannot be specified directly.",
    parameters: {
      type: 'object',
      properties: {
        url: {
          type: 'string',
          description:
            "The URL of the page to refresh. Default: determined by the user's current context",
        },
        waitForLoad: {
          type: 'boolean',
          description:
            'Whether to wait for the page to be fully loaded after refresh. Default: true',
        },
        timeout: {
          type: 'number',
          description: 'Maximum time to wait for page load in milliseconds. Default: 5000',
        },
      },
    },
    returns: {
      type: 'object',
      description: 'Result of the refresh operation',
      properties: {
        success: {
          type: 'boolean',
          description: 'Whether the page was successfully refreshed and loaded',
        },
        error: {
          type: 'string',
          description:
            "Error message if the refresh failed. Common errors include: 'Page load timeout', 'Navigation failed', 'No active page to refresh'",
        },
        data: {
          type: 'object',
          description: 'Additional information about the refresh operation',
          properties: {
            url: {
              type: 'string',
              description: 'The URL of the refreshed page',
            },
            loadTime: {
              type: 'number',
              description: 'Time taken for the page to load in milliseconds',
            },
            status: {
              type: 'string',
              description: 'Final status of the page after refresh (complete, loading, error)',
            },
          },
        },
      },
    },
  },
  {
    name: 'WebToolkit_click',
    description: 'Click an element using its selector or index.',
    parameters: {
      type: 'object',
      properties: {
        selectorOrIndex: {
          type: 'string',
          description:
            'Element identifier - can be either:\n1. Index number (e.g., "0", "1", "2") from the buildDomTree result - use this when you want to click elements from the analyzed DOM tree\n2. CSS selector (e.g., "#submit-button", ".login-form input[type=text]") - use this when you know the page structure and want to target specific elements directly',
        },
      },
      required: ['selectorOrIndex'],
    },
    returns: {
      type: 'object',
      description: 'Result of the click operation',
      properties: {
        success: {
          type: 'boolean',
          description: 'Whether the click was successful',
        },
        error: {
          type: 'string',
          description: 'Error message if click failed',
        },
        data: {
          type: 'object',
          description: 'Click operation details',
          properties: {
            clicked: {
              type: 'boolean',
              description: 'Whether the click action was performed',
            },
            elementFound: {
              type: 'boolean',
              description: 'Whether the element was found in analysis data',
            },
            elementStillExists: {
              type: 'boolean',
              description: 'Whether the element still exists on the page',
            },
            elementInfo: {
              type: 'object',
              description: 'Information about the clicked element',
              properties: {
                tagName: {
                  type: 'string',
                  description: 'Tag name of the element',
                },
                textContent: {
                  type: 'string',
                  description: 'Text content of the element',
                },
              },
            },
          },
        },
      },
    },
  },
  {
    name: 'WebToolkit_sendKeys',
    description:
      'Send strings of special keys like Escape, Backspace, Insert, PageDown, Delete, Enter. Shortcuts such as Control+o, Control+Shift+T are supported as well. This gets used in keyboard.press.',
    parameters: {
      type: 'object',
      properties: {
        keys: {
          type: 'string',
          description:
            'Keys to send. Can be special keys (Escape, Enter, Backspace, Delete, Insert, PageUp, PageDown, Home, End, Tab, Space, ArrowLeft, ArrowUp, ArrowRight, ArrowDown, F1-F12) or key combinations (Control+c, Control+Shift+T, Alt+Tab). Multiple keys can be separated by spaces.',
        },
      },
      required: ['keys'],
    },
    returns: {
      type: 'object',
      description: 'Result of the key sending operation',
      properties: {
        success: {
          type: 'boolean',
          description: 'Whether the keys were successfully sent',
        },
        error: {
          type: 'string',
          description: 'Error message if key sending failed',
        },
        data: {
          type: 'object',
          description: 'Key sending operation details',
          properties: {
            sent: {
              type: 'boolean',
              description: 'Whether the key sending action was performed',
            },
            keys: {
              type: 'string',
              description: 'The keys that were sent',
            },
          },
        },
      },
    },
  },
];

export const SITE_MEMORY_TOOLS: ToolDescription[] = [
  {
    name: 'SiteMemoryToolkit_searchSiteMemory',
    description:
      'Search for site-specific memory based on a hostname and query to find relevant interaction patterns or knowledge. Use this tool when you need additional guidance for website interactions.',
    parameters: {
      type: 'object',
      properties: {
        hostname: {
          type: 'string',
          description: 'The hostname of the website (e.g., "x.com", "xiaohongshu.com")',
        },
        query: {
          type: 'string',
          description: 'The search query to find relevant site memory',
        },
        limit: {
          type: 'number',
          description: 'Maximum number of results to return (default: 5)',
        },
      },
      required: ['hostname', 'query'],
    },
    returns: {
      type: 'array',
      description: 'List of site memory items',
      properties: {
        items: {
          type: 'object',
          description: 'Site memory item',
          properties: {
            id: {
              type: 'string',
              description: 'Memory ID',
            },
            memory: {
              type: 'string',
              description: 'Memory content',
            },
            score: {
              type: 'number',
              description: 'Relevance score',
            },
            metadata: {
              type: 'object',
              description: 'Memory metadata',
            },
          },
        },
      },
    },
  },
];

export const WEB_TOOLKIT_ANALYZE_TOOLS: ToolDescription[] = [
  {
    name: 'WebToolkit_extractText',
    description: 'Extract text from the current page',
    parameters: {
      type: 'object',
      properties: {},
    },
    returns: {
      type: 'object',
      description: 'Markdown content from the page',
      properties: {
        success: {
          type: 'boolean',
          description: 'Whether the operation was successful',
        },
        data: {
          type: 'object',
          description: 'Page content data',
          properties: {
            content: {
              type: 'string',
              description: 'The page content converted to Markdown format',
            },
            url: {
              type: 'string',
              description: 'The URL of the page',
            },
            title: {
              type: 'string',
              description: 'The title of the page',
            },
          },
        },
        error: {
          type: 'string',
          description: 'Error message if the operation failed',
        },
      },
    },
  },
  {
    name: 'WebToolkit_screenshot',
    description: 'Take a screenshot of the current page',
    parameters: {
      type: 'object',
      properties: {
        fullPage: {
          type: 'boolean',
          description: 'Whether to capture the full page or just the viewport',
        },
      },
    },
    returns: {
      type: 'object',
      description: 'Screenshot data',
      properties: {
        dataUrl: {
          type: 'string',
          description:
            'Base64 encoded data of the screenshot or image. If the screenshot is not available, the dataUrl will be an empty string.',
        },
        success: {
          type: 'boolean',
          description: 'Whether the screenshot was successfully taken',
        },
      },
    },
  },
  {
    name: 'WebToolkit_analyzePageDOM',
    description: `
Build DOM tree of the current page or a specific element and its descendants.
Each DOM element will be printed as one line of text while the indentation level indicates the depth of the element in the DOM tree.

Example:
- form#loginForm (action="/login", method="post")
  - input#username [label="Username", type="text", required, placeholder="Enter username"]
  - button.submit (selector="form > button.submit") [label="Login", role="button", interaction="click"]
`,
    parameters: {
      type: 'object',
      properties: {
        selector: {
          type: 'string',
          description:
            'Optional selector to focus the DOM tree on a specific element and its descendants. If not provided, builds tree for all page elements.',
        },
      },
    },
    returns: {
      type: 'object',
      description: 'Element map of the current page',
      properties: {
        success: {
          type: 'boolean',
          description: 'Whether the element map was successfully built',
        },
        error: {
          type: 'string',
          description: 'Error message if element map building failed',
        },
        data: {
          type: 'object',
          description: 'Element map of the current page',
          properties: {
            domTree: {
              type: 'string',
              description: 'DOM tree of the current page',
            },
          },
        },
      },
    },
  },
];
