import { TemplateRegisterRequest } from '@the-agent/shared';
import { DomElementInfo, SummarizeResult } from '~/contents/dom';
import { WebInteractionResult } from '~/types/tools';
import { parseKeySequence } from '~/utils/key';
import { sendKey<PERSON>ombo, sendSing<PERSON><PERSON><PERSON> } from '~/utils/toolkit';

interface DebuggerState {
  attached: boolean;
  tabId: number | null;
  targetId: string | null;
}

export interface ScreenshotResult {
  url: string;
}

interface RefreshPageResult {
  url: string;
  title: string;
  loadTime: string;
  status: string;
}

export interface InputOptions {
  clearFirst?: boolean;
  pressEnterAfterInput?: boolean;
}

export interface SendKeysParams {
  keys: string;
}

interface SendKeysResult {
  sent: boolean;
  keys: string;
}

export class WebToolkit {
  private debuggerState: DebuggerState = {
    attached: false,
    tabId: null,
    targetId: null,
  };

  private hasDebuggerSupport = typeof chrome.debugger?.attach === 'function';
  private targetTabId: number | null = null;

  /**
   * Set the target tab ID for agent operations
   * This ensures agent operations continue on the intended tab even if user switches tabs manually
   */
  setTargetTab(tabId: number): void {
    this.targetTabId = tabId;
  }

  /**
   * Clear the target tab ID, falling back to active tab behavior
   */
  clearTargetTab(): void {
    this.targetTabId = null;
  }

  private async attachDebugger(tabId: number): Promise<void> {
    if (!this.hasDebuggerSupport) {
      return;
    }

    if (this.debuggerState.attached && this.debuggerState.tabId === tabId) {
      return;
    }

    try {
      await chrome.debugger.attach({ tabId }, '1.3');
      this.debuggerState.attached = true;
      this.debuggerState.tabId = tabId;
    } catch (error) {
      console.error('Failed to attach debugger:', error);
      this.hasDebuggerSupport = false; // Disable debugger support
    }
  }

  private async detachDebugger(): Promise<void> {
    if (!this.hasDebuggerSupport || !this.debuggerState.attached || !this.debuggerState.tabId) {
      return;
    }

    try {
      await chrome.debugger.detach({ tabId: this.debuggerState.tabId });
      this.debuggerState.attached = false;
      this.debuggerState.tabId = null;
    } catch (error) {
      console.error('Failed to detach debugger:', error);
    }
  }

  private async sendCommand(method: string, params: object = {}): Promise<object> {
    if (!this.hasDebuggerSupport || !this.debuggerState.attached || !this.debuggerState.tabId) {
      throw new Error('Debugger not available');
    }

    try {
      return await chrome.debugger.sendCommand({ tabId: this.debuggerState.tabId }, method, params);
    } catch (error) {
      console.error(`Failed to execute command ${method}:`, error);
      throw error;
    }
  }

  public async getCurrentTab(): Promise<chrome.tabs.Tab> {
    try {
      // 1. If agent has set a target tab, use that regardless of which tab is currently active
      if (this.targetTabId !== null) {
        try {
          const targetTab = await chrome.tabs.get(this.targetTabId);
          if (targetTab?.id) {
            return targetTab;
          }
        } catch (error) {
          console.error(
            `Target tab ${this.targetTabId} no longer exists, falling back to active tab. error: ${error}`
          );
          this.targetTabId = null; // Clear invalid target tab
        }
      }

      // 2. Fall back to current active tab behavior
      const currentTab = await chrome.tabs.getCurrent();
      if (currentTab?.id) {
        return currentTab;
      }

      // 3. Try querying the active tab of the current window
      const [tab] = await chrome.tabs.query({
        active: true,
        lastFocusedWindow: true,
      });
      if (tab?.id) {
        return tab;
      }

      // 4. Try querying all active tabs
      const tabs = await chrome.tabs.query({
        active: true,
        windowType: 'normal',
      });
      if (tabs.length > 0) {
        return tabs[0];
      }

      throw new Error(
        'No active tab found. Please ensure the extension has the necessary permissions.'
      );
    } catch (error) {
      console.error('Error getting current tab:', error);
      throw error;
    }
  }

  private async executeInTab<T>(
    userFunc: (...args: string[]) => T,
    args: string[] = [],
    tabId?: number
  ): Promise<T> {
    try {
      let tab: chrome.tabs.Tab | null = null;
      if (tabId) {
        tab = await chrome.tabs.get(tabId);
      } else {
        tab = await this.getCurrentTab();
      }
      if (!tab?.id) {
        throw new Error('Tab ID not found. Please ensure the tab is properly loaded.');
      }

      // Execute script
      const [result] = await chrome.scripting.executeScript({
        target: { tabId: tab.id },
        func: userFunc,
        args,
      });

      if (!result) {
        console.error('Script execution returned no result');
        throw new Error('No result returned from script execution');
      }

      // If result is a Promise, wait for it to complete
      if (result.result instanceof Promise) {
        const promiseResult = await result.result;
        // Check if Promise result contains error
        if (
          promiseResult &&
          typeof promiseResult === 'object' &&
          'success' in promiseResult &&
          !promiseResult.success
        ) {
          throw new Error(promiseResult.error || 'Unknown error in Promise result');
        }
        return promiseResult;
      }

      // Check if result contains error
      if (
        result.result &&
        typeof result.result === 'object' &&
        'success' in result.result &&
        !result.result.success
      ) {
        if ('error' in result.result) {
          throw new Error(result.result.error as string);
        }
        throw new Error('Unknown error in result');
      }

      return result.result as T;
    } catch (error) {
      console.error('executeInTab error:', error);
      // Check if this is a permission issue
      if (error instanceof Error && error.toString().includes('Cannot access')) {
        throw new Error(
          'Cannot access this page. Make sure the extension has permissions for this URL.'
        );
      }
      throw error; // Propagate error upward
    }
  }

  async screenshot(): Promise<WebInteractionResult<ScreenshotResult>> {
    try {
      // Get current tab
      const tab = await this.getCurrentTab();

      if (!tab?.id) {
        throw new Error('No active tab found');
      }

      // Use chrome.tabs.captureVisibleTab for screenshot
      const dataUrl = await new Promise<string>((resolve, reject) => {
        chrome.tabs.captureVisibleTab(tab.windowId, { format: 'jpeg', quality: 80 }, dataUrl => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
            return;
          }
          resolve(dataUrl);
        });
      });

      return { success: true, data: { url: dataUrl } };
    } catch (error) {
      console.error('Error taking screenshot:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  async summarize(): Promise<WebInteractionResult<SummarizeResult>> {
    const tab = await this.getCurrentTab();
    if (!tab?.id) {
      return { success: false, error: 'No active tab found' };
    } else {
      return await new Promise<WebInteractionResult<SummarizeResult>>((resolve, reject) => {
        chrome.tabs.sendMessage(
          tab.id!,
          {
            name: 'summarize-page',
          },
          response => {
            if (chrome.runtime.lastError) {
              reject(new Error(chrome.runtime.lastError.message));
              return;
            }
            resolve(response);
          }
        );
      });
    }
  }

  async buildDomTemplate(): Promise<WebInteractionResult<TemplateRegisterRequest>> {
    const tab = await this.getCurrentTab();
    if (!tab?.id) {
      return { success: false, error: 'No active tab found' };
    } else {
      return await new Promise<WebInteractionResult<TemplateRegisterRequest>>((resolve, reject) => {
        chrome.tabs.sendMessage(
          tab.id!,
          {
            name: 'build-dom-template',
          },
          response => {
            if (chrome.runtime.lastError) {
              reject(new Error(chrome.runtime.lastError.message));
              return;
            }
            resolve(response);
          }
        );
      });
    }
  }

  async buildDomTree(selector?: string): Promise<WebInteractionResult<TemplateRegisterRequest>> {
    const tab = await this.getCurrentTab();
    if (!tab?.id) {
      return { success: false, error: 'No active tab found' };
    } else {
      return await new Promise<WebInteractionResult<TemplateRegisterRequest>>((resolve, reject) => {
        chrome.tabs.sendMessage(
          tab.id!,
          {
            name: 'build-dom-tree',
            selector,
          },
          response => {
            if (chrome.runtime.lastError) {
              reject(new Error(chrome.runtime.lastError.message));
              return;
            }
            resolve(response);
          }
        );
      });
    }
  }

  async refreshPage(
    url?: string,
    waitForLoad: boolean = true,
    timeout: number = 5000
  ): Promise<WebInteractionResult<RefreshPageResult>> {
    try {
      // Get current tab
      const tab = await this.getCurrentTab();
      if (!tab?.id) {
        throw new Error('No active tab found');
      }

      // Record start time
      const startTime = Date.now();

      await this.executeInTab(
        (url?: string) => {
          return new Promise<WebInteractionResult<undefined>>(resolve => {
            if (url) {
              window.location.href = url;
            } else {
              location.reload();
            }
            resolve({ success: true, data: undefined });
          });
        },
        url ? [url] : []
      );

      // Wait for page load completion if needed
      if (waitForLoad) {
        await new Promise<void>((resolve, reject) => {
          const timeoutId = setTimeout(() => {
            chrome.tabs.onUpdated.removeListener(listener);
            reject(new Error('Page load timeout'));
          }, timeout);

          const listener = (tabId: number, info: chrome.tabs.TabChangeInfo) => {
            if (tabId === tab.id && info.status === 'complete') {
              clearTimeout(timeoutId);
              chrome.tabs.onUpdated.removeListener(listener);
              resolve();
            }
          };

          chrome.tabs.onUpdated.addListener(listener);
        });
      }

      // Get page state
      const pageState = await this.executeInTab<{ url: string; title: string; readyState: string }>(
        () => {
          return {
            url: window.location.href,
            title: document.title,
            readyState: document.readyState,
          };
        }
      );

      // Calculate load time
      const loadTime = Date.now() - startTime;

      return {
        success: true,
        data: {
          url: pageState.url,
          title: pageState.title,
          loadTime: loadTime.toString(),
          status: pageState.readyState,
        },
      };
    } catch (error) {
      console.error('Error refreshing page:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  async runInContentScript<T>(
    args: { name: string } & Record<string, any>
  ): Promise<WebInteractionResult<T>> {
    const tab = await this.getCurrentTab();
    if (!tab?.id) {
      return { success: false, error: 'No active tab found' };
    } else {
      return await new Promise<WebInteractionResult<T>>((resolve, reject) => {
        chrome.tabs.sendMessage(tab.id!, args, response => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
            return;
          }
          resolve(response);
        });
      });
    }
  }

  async click(selectorOrIndex: string): Promise<WebInteractionResult<string>> {
    return await this.runInContentScript<string>({
      name: 'click-element',
      selectorOrIndex,
    });
  }

  async scrollTo(selectorOrIndex: string): Promise<WebInteractionResult<string>> {
    return await this.runInContentScript<string>({
      name: 'scroll-to-element',
      selectorOrIndex,
    });
  }

  async input(
    selectorOrIndex: string,
    value: string,
    options?: InputOptions
  ): Promise<WebInteractionResult<string>> {
    try {
      const tab = await this.getCurrentTab();
      if (!tab?.id) {
        throw new Error('No active tab found');
      }

      // 1. Get element info using hash key
      const elementInfo = await this.runInContentScript<DomElementInfo>({
        name: 'get-element',
        selectorOrIndex,
      });

      if (!elementInfo || !elementInfo.success) {
        // Try to get more debugging information
        let debugInfo = '';
        try {
          const domAnalysis = await this.runInContentScript<string>({
            name: 'build-dom-tree',
          });
          if (domAnalysis && domAnalysis.success) {
            debugInfo = '\n\nAvailable elements on page:\n' + domAnalysis.data;
          }
        } catch (debugError) {
          console.warn('Failed to get debug info:', debugError);
        }

        return {
          success: false,
          error: (elementInfo?.error || 'Failed to get element information') + debugInfo,
        };
      }
      if (!elementInfo.data?.visible) {
        return {
          success: false,
          error: 'Element is not visible',
        };
      }

      await this.attachDebugger(tab.id);
      await this.sendCommand('Input.dispatchMouseEvent', {
        type: 'mouseMoved',
        x: elementInfo.data.x,
        y: elementInfo.data.y,
        button: 'none',
        buttons: 0,
      });
      await this.sendCommand('Input.dispatchMouseEvent', {
        type: 'mousePressed',
        x: elementInfo.data.x,
        y: elementInfo.data.y,
        button: 'left',
        buttons: 1,
        clickCount: 1,
      });
      await this.sendCommand('Input.dispatchMouseEvent', {
        type: 'mouseReleased',
        x: elementInfo.data.x,
        y: elementInfo.data.y,
        button: 'left',
        buttons: 0,
        clickCount: 1,
      });

      const result = await this.runInContentScript<string>({
        name: 'input-element',
        selectorOrIndex,
        value,
        clearFirst: options?.clearFirst,
      });

      if (options?.pressEnterAfterInput) {
        await this.sendCommand('Input.dispatchKeyEvent', {
          type: 'keyDown',
          key: 'Enter',
          code: 'Enter',
          windowsVirtualKeyCode: 13,
          nativeVirtualKeyCode: 13,
          text: '\r',
        });
        await this.sendCommand('Input.dispatchKeyEvent', {
          type: 'keyUp',
          key: 'Enter',
          code: 'Enter',
          windowsVirtualKeyCode: 13,
          nativeVirtualKeyCode: 13,
        });
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      await this.detachDebugger();
      return result;
    } catch (error) {
      console.error('Error in inputElementByKey:', error);
      await this.detachDebugger();

      // Provide helpful suggestions for common errors
      let errorMessage = error instanceof Error ? error.message : String(error);
      if (errorMessage.includes('Element with selector') && errorMessage.includes('not found')) {
        errorMessage +=
          '\n\nSuggestions:\n1. Use "analyzePageDOM" to find the correct selector\n2. Try common selectors like "textarea", "[contenteditable=true]", or "[role=textbox]"\n3. Check if the element is dynamically loaded and wait for it to appear';
      }

      return {
        success: false,
        error: errorMessage,
      };
    }
  }

  async sendKeys({ keys }: SendKeysParams): Promise<WebInteractionResult<SendKeysResult>> {
    try {
      const tab = await this.getCurrentTab();
      if (!tab?.id) {
        throw new Error('No active tab found');
      }

      // Attach debugger
      await this.attachDebugger(tab.id);

      // Parse the keys string and send appropriate key events
      const keySequences = parseKeySequence(keys);

      for (const keySequence of keySequences) {
        if (keySequence.isCombo) {
          // Handle key combinations like Control+c, Control+Shift+T
          await sendKeyCombo(keySequence.keys, this.sendCommand.bind(this));
        } else {
          // Handle single keys
          await sendSingleKey(keySequence.keys[0], this.sendCommand.bind(this));
        }
      }

      // Detach debugger
      await this.detachDebugger();

      return {
        success: true,
        data: {
          sent: true,
          keys: keys,
        },
      };
    } catch (error) {
      console.error('Error in sendKeys:', error);
      await this.detachDebugger();
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }
}
