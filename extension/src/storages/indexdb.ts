import { Conversation } from '../types/conversations';
import <PERSON><PERSON>, { Table } from 'dexie';
import { Model } from '~/types';
import { PROVIDER_MODELS } from '../configs/llm';
import { ConversationType, Message, Installation } from '@the-agent/shared';
import { DEFAULT_MODEL, SYSTEM_MODEL_ID } from '../configs/common';
import { env } from '../configs/env';
import { MiniAppLocal as MiniApp } from '~/types/miniapp';

export interface UserInfo {
  id: string;
  username?: string;
  email?: string | null;
  api_key_enabled: boolean;
  api_key: string;
  credits: string; // Credits stored as string in IndexedDB
  created_at: string;
  updated_at: string;
  selectedModelId: string;
  photoURL?: string;
  permission?: {
    can_save_site_message: boolean;
  };
  active?: boolean;
}

export interface Template {
  id: number;
  domain: string;
  path_pattern: string;
}

export interface VersionedMessage extends Message {
  version: number;
}

class MystaDB extends Dexie {
  getCurrentModel() {
    throw new Error('Method not implemented.');
  }
  conversations!: Table<Conversation>;
  messages!: Table<VersionedMessage>;
  users!: Table<UserInfo>;
  models!: Table<Model>;
  apps!: Table<MiniApp>;
  templates!: Table<Template>;

  constructor() {
    super('mysta-agent');

    this.version(9)
      .stores({
        conversations:
          'id, *messages, user_id, last_selected_at, type, [user_id+type+last_selected_at]',
        messages: 'id, conversation_id, run_id, task_id, [run_id+task_id]',
        users:
          'id, api_key, api_key_enabled, credits, created_at, email, username, photoURL, updated_at, active',
        models: 'id, userId, type, [userId+id]',
        tasks: '[id+workflowId], conversationId, parentTaskId, depth',
        apps: 'id, conversation_id',
        templates: 'id, domain',
      })
      .upgrade(async tx => {
        // Ensure all existing conversations have a default type
        await tx
          .table('conversations')
          .toCollection()
          .modify((c: Conversation) => {
            if (!c.type) c.type = 'default';
          });
      });

    // Add index definitions
    this.messages.hook('creating', function (_, obj) {
      if (!obj.id) {
        obj.id = Date.now();
      }
    });
  }

  // User operations
  async saveUser(user: UserInfo): Promise<void> {
    await this.users.put(user);
  }

  async deleteUser(userId: string): Promise<void> {
    await this.users.delete(userId);
  }

  // Model operations
  async getUserModels(userId: string) {
    if (!userId) {
      throw new Error('User ID is required to get models');
    }
    try {
      return await this.models.where('userId').equals(userId).toArray();
    } catch (error) {
      console.error('Error getting user models:', error);
      throw new Error(
        `Failed to get user models: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  async addOrUpdateModel(model: {
    id: string;
    type: string;
    name: string;
    userId: string;
    apiKey: string;
    apiUrl: string;
  }) {
    const existingModel = await this.models
      .where('id')
      .equals(model.id)
      .and(m => m.userId === '')
      .first();
    if (existingModel) {
      await this.models.put({ ...existingModel, ...model });
    } else {
      await this.models.put(model);
    }
  }

  async deleteModel(modelId: string) {
    await this.models.delete(modelId);
  }

  // Conversation operations
  async saveConversation(conversation: Conversation): Promise<void> {
    await this.conversations.put(conversation);
  }

  async updateConversation(id: number, update: Partial<Conversation>): Promise<void> {
    await this.conversations.update(id, update);
  }

  async getConversationWithoutMessages(type: ConversationType): Promise<Conversation | null> {
    const candidates = await this.conversations.where('type').equals(type).toArray();
    candidates.sort((a, b) => (b.last_selected_at ?? 0) - (a.last_selected_at ?? 0));
    for (const conv of candidates) {
      const anyMsg = await this.messages
        .where('conversation_id')
        .equals(conv.id)
        .limit(1)
        .toArray();
      if (anyMsg.length === 0) {
        return { ...conv, messages: [] } as Conversation;
      }
    }
    return null;
  }

  async getConversation(id: number): Promise<Conversation | null> {
    const conversation = await this.conversations.get(id);
    return conversation || null;
  }

  async getAllConversations(userId: string, type: ConversationType): Promise<Conversation[]> {
    // Efficiently query and sort by last_selected_at DESC within (user_id, type)
    const queryType = (type ?? 'default') as ConversationType;
    const conversations = await this.conversations
      .where('[user_id+type+last_selected_at]')
      .between([userId, queryType, Dexie.minKey], [userId, queryType, Dexie.maxKey])
      .reverse()
      .toArray();

    // Join first message into each conversation for title/preview
    const firstMessages = await Promise.all(
      conversations.map(conv => this.getFirstMessage(conv.id))
    );
    return conversations.map((conv, idx) => ({
      ...conv,
      messages: firstMessages[idx] ? [firstMessages[idx] as VersionedMessage] : [],
    }));
  }

  async deleteConversation(id: number): Promise<void> {
    await this.conversations.delete(id);
  }

  // Message operations
  async saveMessage(message: Message): Promise<void> {
    await this.messages.put({ ...message, version: 1 });
  }

  async saveMessages(messages: Message[]): Promise<void> {
    await this.messages.bulkPut(messages.map(msg => ({ ...msg, version: 1 })));
  }

  async getMessagesByConversation(
    conversationId: number,
    runId?: string,
    taskId?: string
  ): Promise<VersionedMessage[]> {
    const query = this.messages.where('conversation_id').equals(conversationId);
    if (runId) {
      query.and(msg => msg.run_id === runId);
    }
    if (taskId) {
      query.and(msg => msg.task_id === taskId);
    }
    const messages = await query.sortBy('id');
    return messages || [];
  }

  async getMessage(messageId: number): Promise<VersionedMessage | null> {
    const message = await this.messages.get(messageId);
    return message || null;
  }

  async getFirstMessage(conversationId: number): Promise<VersionedMessage | null> {
    const first = await this.messages.where('conversation_id').equals(conversationId).first();
    return first ?? null;
  }

  async getLastMessage(conversationId: number): Promise<VersionedMessage | null> {
    const last = await this.messages.where('conversation_id').equals(conversationId).last();
    return last ?? null;
  }

  async saveErrorMessage(conversationId: number, errorMsg: string): Promise<void> {
    const lastMessage = await this.getLastMessage(conversationId);
    if (!lastMessage || lastMessage.status === 'error') return;
    lastMessage.status = 'error';
    lastMessage.error = errorMsg;
    await this.saveMessage(lastMessage);
  }

  async getLastMessageVersion(conversationId: number): Promise<number> {
    // Get the last message in the conversation
    const messages = await this.messages
      .where('conversation_id')
      .equals(conversationId)
      .reverse()
      .limit(1)
      .toArray();

    return messages.length > 0 ? messages[0].version || 0 : 0;
  }

  async getLastInteractionTime(): Promise<number> {
    // Using reverse() and first() to get the highest id directly from the database
    const lastConversation = await this.conversations.orderBy('last_selected_at').reverse().first();
    return lastConversation?.last_selected_at || 0;
  }

  async deleteMessagesByConversation(conversationId: number): Promise<void> {
    await this.messages.where('conversation_id').equals(conversationId).delete();
  }

  // Related messages operations
  async getRelatedMessagesWithContext(
    messageIds: number[],
    conversationId: number
  ): Promise<Message[][]> {
    const allMessages = await this.messages
      .where('conversation_id')
      .equals(conversationId)
      .sortBy('id');

    const contextMessages: Message[][] = [];

    for (const messageId of messageIds) {
      const targetIndex = allMessages.findIndex((m: Message) => m.id === messageId);
      if (targetIndex === -1) continue;

      // Get 2 messages before and after the target message
      const start = Math.max(0, targetIndex - 2);
      const end = Math.min(allMessages.length, targetIndex + 3);
      contextMessages.push(allMessages.slice(start, end));
    }
    return contextMessages;
  }

  async getRecentMessages(
    conversationId: number,
    limit: number,
    taskFilter?: {
      runId?: string | null;
      taskId?: string | null;
    }
  ): Promise<Message[]> {
    let messages = this.messages
      .where('conversation_id')
      .equals(conversationId)
      // only include finished messages
      .filter((message: Message) => message.status === 'error' || message.status === 'completed');
    // If taskId is provided, only include messages with matching task_id
    if (taskFilter) {
      messages = messages.filter(
        message => message.run_id === taskFilter.runId && message.task_id === taskFilter.taskId
      );
    }
    const results = await messages.reverse().sortBy('id');
    return results.slice(0, limit);
  }

  async findLastToolCall(conversationId: number, toolName: string): Promise<Message | null> {
    const messages = await this.messages
      .where('conversation_id')
      .equals(conversationId)
      .filter((msg: Message) => msg.role === 'tool')
      .filter((msg: Message) => msg.name === toolName)
      .reverse()
      .limit(1)
      .toArray();
    return messages.length > 0 ? messages[0] : null;
  }

  async getMessagesByRunIdAndTaskId(runId: string, taskId: string): Promise<VersionedMessage[]> {
    const messages = await this.messages
      .where('[run_id+task_id]')
      .equals([runId, taskId])
      .sortBy('id');
    return messages || [];
  }

  // Conversation and Messages operations
  async backfill(conversations: Conversation[]): Promise<void> {
    try {
      await this.transaction('rw', [this.conversations, this.messages], async () => {
        for (const conversation of conversations) {
          const { messages, ...rest } = conversation;
          const existingConv = await this.conversations.get(conversation.id);
          if (!existingConv) {
            await this.conversations.put(rest);
          }

          if (messages && messages.length > 0) {
            const validMessages = messages
              .filter(msg => msg && msg.id)
              .map(msg => ({
                ...msg,
                conversation_id: conversation.id,
                version: 1,
              }));
            await this.messages.bulkPut(validMessages);
          }
        }
      });
    } catch (error) {
      throw new Error(
        `Failed to save conversations and messages: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  async clearAllConversations() {
    await this.transaction('rw', [this.conversations, this.messages], async () => {
      await this.messages.clear();
      await this.conversations.clear();
    });
  }

  async clearUserData(userId: string) {
    await this.transaction('rw', [this.conversations, this.messages], async () => {
      // Delete all messages from conversations belonging to this user
      const userConversations = await this.conversations.where('user_id').equals(userId).toArray();

      const conversationIds = userConversations.map(conv => conv.id);

      // Delete all messages from these conversations
      await this.messages.where('conversation_id').anyOf(conversationIds).delete();

      // Delete all conversations for this user
      await this.conversations.where('user_id').equals(userId).delete();
    });
  }

  async getCurrentUser(): Promise<UserInfo | null> {
    const user = await this.users.filter((u: UserInfo) => u.active === true).first();
    return user ?? null;
  }

  async getSelectModel(): Promise<Model> {
    const user = await this.getCurrentUser();
    if (!user) {
      throw new Error('User not found');
    }

    const selectedModel = await this.models.where('id').equals(user.selectedModelId).first();
    if (!selectedModel) {
      throw new Error('Model not found');
    }
    if (selectedModel.id === 'system') {
      selectedModel.name = DEFAULT_MODEL;
      selectedModel.apiKey = user.api_key;
      selectedModel.apiUrl = env.BACKEND_URL + '/v1';
    }
    return selectedModel;
  }

  async saveOrUpdateUser(user: UserInfo): Promise<void> {
    if (!user || !user.id) {
      throw new Error('Invalid user data: user ID is required');
    }

    try {
      const now = new Date().toISOString();
      const existing = await this.users.get(user.id);

      const systemModel = {
        id: SYSTEM_MODEL_ID,
        type: 'system',
        name: 'Mysta',
        userId: user.id,
        apiKey: '',
        apiUrl: '',
      };

      // ensure only one current user
      await this.users.toCollection().modify((u: UserInfo) => {
        u.active = false;
      });

      const userToSave: UserInfo = {
        ...user,
        active: true,
      };

      if (existing) {
        await this.users.put({
          ...userToSave,
          selectedModelId: SYSTEM_MODEL_ID,
          created_at: existing.created_at,
          updated_at: now,
        });
      } else {
        await this.users.put({ ...userToSave, created_at: now, updated_at: now });
      }

      await this.models.put(systemModel);
    } catch (error) {
      console.error('Error saving/updating user:', error);
      throw new Error(
        `Failed to save/update user: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  async saveApplication(app: MiniApp) {
    await this.apps.put(app);
  }

  async deploy(app: MiniApp, installation: Installation) {
    installation.deployed_at = Date.now();
    app.installation = installation;
    await this.apps.put(app);
  }

  async revert(app: MiniApp, version: number) {
    const filtered = app.history.filter(i => i.deployed_at && i.deployed_at <= version);
    if (filtered.length === 0 || filtered[filtered.length - 1]!.deployed_at !== version) {
      throw new Error('version not found');
    }
    app.history = filtered;
    app.installation = filtered[filtered.length - 1];
    await this.apps.put(app);
  }

  async getAllApps(filter: 'deployed' | 'developing' | 'all'): Promise<MiniApp[]> {
    const allApps = await this.apps.toArray();
    // Filter out archived apps by default
    const nonArchivedApps = allApps.filter(app => app.status !== 'archived');

    if (filter === 'deployed') {
      return nonArchivedApps.filter(app => app.installation !== undefined);
    } else if (filter === 'developing') {
      return nonArchivedApps.filter(app => app.installation == undefined);
    } else {
      return nonArchivedApps;
    }
  }

  async getAllInstallations(): Promise<(Installation & { appId: number })[]> {
    const allApps = await this.apps.toArray();
    return allApps
      .filter(app => app.installation !== undefined)
      .map(app => ({
        appId: app.id,
        ...app.installation!,
      }));
  }

  async getArchivedApps(): Promise<MiniApp[]> {
    const allApps = await this.apps.toArray();
    return allApps.filter(app => app.status === 'archived');
  }

  async archiveMiniapp(id: number): Promise<void> {
    const app = await this.apps.get(id);
    if (!app) {
      throw new Error('MiniApp not found');
    }
    app.status = 'archived';
    await this.apps.put(app);
  }

  async activateMiniapp(id: number): Promise<void> {
    const app = await this.apps.get(id);
    if (!app) {
      throw new Error('MiniApp not found');
    }
    app.status = 'active';
    await this.apps.put(app);
  }

  async getUserByApiKey(apiKey: string): Promise<UserInfo | null> {
    const user = await this.users.where('api_key').equals(apiKey).first();
    return user || null;
  }

  async initModels(userId: string): Promise<void> {
    const allModels = PROVIDER_MODELS.flatMap(provider =>
      provider.models.map(model => ({
        ...model,
        userId,
        name: model.id === 'system' ? DEFAULT_MODEL : model.name,
        type: model.id === 'system' ? 'Default' : provider.type,
        apiKey: model.id === 'system' ? '' : '',
        apiUrl: model.id === 'system' ? '' : model.apiUrl,
      }))
    );
    await this.models.bulkPut(allModels);
  }

  async saveTemplate(template: Template): Promise<void> {
    await this.templates.put(template);
  }

  async saveTemplates(templates: Template[]): Promise<void> {
    await this.templates.bulkPut(templates);
  }

  async getTemplate(url: string): Promise<Template | null> {
    const domain = new URL(url).hostname;
    const matchPathPattern = (pattern: string, path: string): boolean => {
      const pTokens = pattern.split('/').filter(Boolean);
      const pathTokens = path.split('/').filter(Boolean);
      if (pTokens.length !== pathTokens.length) return false;

      return pTokens.every((pt, i) =>
        pt.startsWith('{') && pt.endsWith('}') ? true : pt === pathTokens[i]
      );
    };
    const templates = await this.templates.where('domain').equals(domain).toArray();
    const path = new URL(url).pathname;
    for (const template of templates) {
      if (matchPathPattern(template.path_pattern, path)) {
        return template;
      }
    }
    return null;
  }

  async getMiniapp(id: number): Promise<MiniApp | null> {
    const app = await this.apps.get(id);
    return app || null;
  }

  async updateMiniapp(id: number, update: Partial<MiniApp>): Promise<void> {
    await this.apps.update(id, update);
  }

  async getMiniappByConversationId(conversationId: number): Promise<MiniApp | null> {
    const app = await this.apps.where('conversation_id').equals(conversationId).first();
    return app || null;
  }
}

let dbInstance = new MystaDB();

export async function resetDB() {
  await dbInstance.delete();
  dbInstance = new MystaDB();
  (window as unknown as { mystaDB: MystaDB }).mystaDB = dbInstance;
  return dbInstance;
}

const dbProxy = new Proxy(
  {},
  {
    get(_, prop) {
      return dbInstance[prop as keyof MystaDB];
    },
  }
);

export const db = dbProxy as MystaDB;
