import {
  Message,
  MessageRole,
  MemoryConfig,
  MemoryType,
  ExecutionStep,
  ChatAgent,
  MemoryMetadata,
  extractMessageText,
  extractUserMessageText,
} from '@the-agent/shared';
import { VectorStoreInterface, LLMRuntimeInterface } from '../types/memory';
import { MEMORY_AGENT_CONTEXT_INSTRUCTIONS } from '../agents/prompt';

/**
 * Generate user message with context for memory processing
 */
export function generateUserMessageWithContext(messages: Message[]): string {
  // Only include recent messages to avoid context overflow
  const recentMessages = messages.slice(-10); // Last 10 messages
  const context = recentMessages.map(formatMessage).join('\n');

  // Extract user instructions from messages
  const userInstructions = extractUserInstructions(messages);

  return `\
# 🧠 Conversation Context:
${context}

# 📌 Instructions:
${MEMORY_AGENT_CONTEXT_INSTRUCTIONS}

# 👤 User Instructions:
${userInstructions}
  `.trim();
}

/**
 * Extract user instructions from messages
 */
function extractUserInstructions(messages: Message[]): string {
  const userMessages = messages.filter(msg => msg.role === 'user');
  if (userMessages.length === 0) return 'No user instructions found';

  // Get the most recent user message for primary instruction
  const latestUserMessage = userMessages[userMessages.length - 1];
  const latestText = extractUserMessageText(latestUserMessage);

  if (userMessages.length === 1) {
    // Single user message
    return latestText
      ? latestText.substring(0, 200) + (latestText.length > 200 ? '...' : '')
      : 'No instruction';
  }

  // Multiple user messages - include context
  const contextMessages = userMessages.slice(-3); // Last 3 user messages
  const contextText = contextMessages
    .map(msg => extractUserMessageText(msg))
    .filter(Boolean)
    .join(' | ');

  return contextText.substring(0, 300) + (contextText.length > 300 ? '...' : '');
}

/**
 * Format individual message for context
 */
function formatMessage(msg: Message): string {
  if (msg.role === 'user') {
    return `user: ${extractUserMessageText(msg)}`;
  }

  if (msg.role === 'assistant') {
    const reasoning = (msg as { reasoning?: string }).reasoning;
    const reasoningText = reasoning ? `\nReasoning: ${reasoning}` : '';
    return `assistant: ${msg.content}${reasoningText}`;
  }

  if (msg.role === 'tool') {
    const toolName = (msg as { name?: string }).name || 'unknown_tool';
    const toolCallId = (msg as { tool_call_id?: string }).tool_call_id || '';
    let result = 'No result';

    if (msg.tool_call_result) {
      if (msg.tool_call_result.success) {
        result = msg.tool_call_result.data ? JSON.stringify(msg.tool_call_result.data) : 'Success';
      } else {
        result = msg.tool_call_result.error || 'Failed';
      }
    } else if (msg.content) {
      try {
        const content = JSON.parse(msg.content);

        if (content.success) {
          if (content.data) {
            // Show data directly
            if (typeof content.data === 'string') {
              result = content.data;
            } else {
              result = JSON.stringify(content.data);
            }
          } else {
            result = 'Completed';
          }
        } else {
          result = content.error || 'Failed';
        }
      } catch {
        result = msg.content || 'No result';
      }
    }

    return `tool[${toolName}${toolCallId ? `:${toolCallId}` : ''}]: ${result}`;
  }

  if (msg.tool_calls && msg.tool_calls.length > 0) {
    const toolCalls = msg.tool_calls
      .map(tc => {
        const name = tc.function?.name || 'unknown';
        const args = tc.function?.arguments || '{}';
        return `${name}(${args})`;
      })
      .join(', ');
    return `tool_calls: [${toolCalls}]`;
  }

  return JSON.stringify(msg);
}

/**
 * Process memory asynchronously based on type
 */
export async function processMemoryAsync(
  messages: Message[],
  config: MemoryConfig,
  memoryAgent: ChatAgent,
  vectorStore: VectorStoreInterface,
  llmRuntime: LLMRuntimeInterface
): Promise<void> {
  try {
    if (config.metadata?.memoryType === 'site') {
      // For site memory, process directly without LLM
      const content = messages.map(msg => extractMessageText(msg)).join('\n');
      if (content.trim()) {
        await processRawMemoryAsync(content, config.metadata || {}, vectorStore, llmRuntime);
      }
    } else {
      await processSemanticMemory(messages, config, memoryAgent);
      await processProceduralMemory(messages, config, vectorStore, llmRuntime);
    }
  } catch (error) {
    console.error('Memory processing error:', error);
  }
}

/**
 * Process semantic memory using memory agent
 */
async function processSemanticMemory(
  messages: Message[],
  config: MemoryConfig,
  memoryAgent: ChatAgent
): Promise<void> {
  const enhancedContext = generateUserMessageWithContext(messages);
  const userMessage = createUserMessage(enhancedContext, messages);

  await memoryAgent.run(userMessage, {
    toolOptions: {
      filters: config?.filters || {},
      metadata: config?.metadata || {},
    },
  });
}

/**
 * Process site memory directly without LLM
 */
export async function processRawMemoryAsync(
  data: string,
  metadata: MemoryMetadata,
  vectorStore?: VectorStoreInterface,
  llmRuntime?: LLMRuntimeInterface
): Promise<void> {
  try {
    if (!vectorStore || !llmRuntime) {
      return;
    }

    const embedding = await llmRuntime.embed(data);
    const payload = {
      text: data,
      metadata: {
        ...metadata,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
    };

    await vectorStore.insert([embedding], [payload]);
  } catch (error) {
    console.error('Site memory direct processing error:', error);
  }
}

/**
 * Process procedural memory by generating structured summary from tool messages only
 */
async function processProceduralMemory(
  messages: Message[],
  config: MemoryConfig,
  vectorStore: VectorStoreInterface,
  llmRuntime: LLMRuntimeInterface
): Promise<void> {
  try {
    const toolMessages = messages.filter(msg => msg.role === 'tool');
    if (toolMessages.length === 0) {
      return;
    }

    const summary = await generateProceduralSummary(toolMessages);
    const embedding = await llmRuntime.embed(summary);
    const payload = createProceduralMemoryPayload(summary, config);

    await vectorStore.insert([embedding], [payload]);
  } catch (error) {
    console.error('processProceduralMemory error:', error);
    throw error;
  }
}

/**
 * Create payload for procedural memory storage
 */
function createProceduralMemoryPayload(summary: string, config: MemoryConfig) {
  const now = new Date().toISOString();

  return {
    text: summary,
    metadata: {
      ...config?.metadata,
      memoryType: 'procedural' as MemoryType,
      data: summary,
      created_at: now,
      updated_at: now,
    },
  };
}

/**
 * Create user message for memory processing
 */
function createUserMessage(content: string, messages: Message[]): Message {
  return {
    id: Date.now(),
    conversation_id: messages[0]?.conversation_id,
    role: 'user' as MessageRole,
    content,
  };
}

/**
 * Generate structured summary from tool execution steps
 */
async function generateProceduralSummary(toolMessages: Message[]): Promise<string> {
  const taskGoal = extractTaskGoalFromToolMessages(toolMessages);
  const progressStatus = calculateToolProgress(toolMessages);
  const steps = extractToolExecutionSteps(toolMessages);

  return formatProceduralSummary(taskGoal, progressStatus, steps);
}

/**
 * Format procedural memory summary for tool execution
 */
function formatProceduralSummary(
  taskGoal: string,
  progressStatus: string,
  steps: Omit<ExecutionStep, 'stepNumber' | 'timestamp'>[]
): string {
  const stepsSection = steps.map((step, index) => formatExecutionStep(step, index + 1)).join('\n');

  return `
## Tool Execution Summary
**Task Objective**: ${taskGoal}
**Progress Status**: ${progressStatus}
**Tool Steps**:
${stepsSection}
  `.trim();
}

/**
 * Format individual execution step
 */
function formatExecutionStep(
  step: Omit<ExecutionStep, 'stepNumber' | 'timestamp'>,
  stepNumber: number
): string {
  const optionalFields = [
    step.keyFindings && `**Key Findings**: ${step.keyFindings}`,
    step.navigationHistory && `**Navigation History**: ${step.navigationHistory}`,
    step.errors && `**Errors & Challenges**: ${step.errors}`,
  ]
    .filter(Boolean)
    .join('\n   ');

  const optionalSection = optionalFields ? `${optionalFields}\n   ` : '';

  return `
${stepNumber}. **Tool Action**: ${step.agentAction}
   **Action Result**: ${step.actionResult}
   ${optionalSection}**Status**: ${step.currentContext}
`;
}

/**
 * Extract task goal from tool messages (look for context in tool results)
 */
function extractTaskGoalFromToolMessages(toolMessages: Message[]): string {
  // Try to find task goal in the conversation context
  for (const msg of toolMessages) {
    if (msg.content) {
      try {
        const result = JSON.parse(msg.content);
        if (result.context || result.task || result.goal) {
          return result.context || result.task || result.goal;
        }
      } catch {
        // Continue to next message
      }
    }
  }

  // Infer task goal from tool actions
  const toolActions = toolMessages.map(msg => msg.name).filter(Boolean);
  if (toolActions.length > 0) {
    const uniqueActions = [...new Set(toolActions)];
    const actionCount = uniqueActions.length;

    if (actionCount === 1) {
      const action = uniqueActions[0];
      if (action && action.includes('extractText')) return 'Extracting page content';
      if (action && action.includes('analyzePageDOM')) return 'Analyzing page structure';
      if (action && action.includes('input')) return 'Interacting with page elements';
      if (action && action.includes('openTab')) return 'Managing browser tabs';
      return `Executing ${action || 'unknown'}`;
    } else {
      return `Executing ${actionCount} operations`;
    }
  }

  return 'Tool execution task';
}

/**
 * Calculate progress status for tool messages
 */
function calculateToolProgress(toolMessages: Message[]): string {
  const totalSteps = toolMessages.length;
  const completedSteps = toolMessages.filter(msg => {
    if (!msg.content) return false;

    try {
      const result = JSON.parse(msg.content);
      return result.success === true;
    } catch {
      // If parsing fails, check if content indicates success
      const contentStr = msg.content.toString();
      return contentStr.includes('"success":true') || contentStr.includes('Success:');
    }
  }).length;

  const percentage = totalSteps > 0 ? Math.round((completedSteps / totalSteps) * 100) : 0;
  return `${percentage}% complete (${completedSteps}/${totalSteps} tool steps)`;
}

/**
 * Extract execution steps from tool messages only
 */
function extractToolExecutionSteps(
  toolMessages: Message[]
): Omit<ExecutionStep, 'stepNumber' | 'timestamp'>[] {
  const steps: Omit<ExecutionStep, 'stepNumber' | 'timestamp'>[] = [];

  for (const msg of toolMessages) {
    const step = createEmptyStep();

    try {
      const toolResult = JSON.parse(msg.content || '{}');
      step.agentAction = `${msg.name || 'unknown_tool'}`;

      // Show actual tool results
      if (toolResult.success) {
        if (toolResult.data) {
          // Just show the data directly
          if (typeof toolResult.data === 'string') {
            step.actionResult = toolResult.data;
          } else {
            step.actionResult = JSON.stringify(toolResult.data);
          }
        } else {
          step.actionResult = 'Completed';
        }
        step.currentContext = 'Success';
      } else {
        step.actionResult = toolResult.error || 'Failed';
        step.currentContext = 'Failed';
      }

      if (toolResult.keyFindings) step.keyFindings = toolResult.keyFindings;
      if (toolResult.navigationHistory) step.navigationHistory = toolResult.navigationHistory;
      if (toolResult.errors) step.errors = toolResult.errors;

      steps.push(step);
    } catch (error) {
      step.agentAction = `${msg.name || 'unknown_tool'}`;

      // Better error handling for parsing failures
      if (msg.content) {
        const contentStr = msg.content.toString();

        // Extract data directly from content string
        if (contentStr.includes('"success":true')) {
          // Try to extract data field
          const dataMatch = contentStr.match(/"data":\s*"([^"]+)"/);
          if (dataMatch) {
            step.actionResult = dataMatch[1];
          } else {
            // Show the raw content
            step.actionResult = contentStr;
          }
          step.currentContext = 'Success';
        } else if (contentStr.includes('"success":false')) {
          const errorMatch = contentStr.match(/"error":\s*"([^"]+)"/);
          step.actionResult = errorMatch ? errorMatch[1] : 'Failed';
          step.currentContext = 'Failed';
        } else {
          // Show the raw content
          step.actionResult = contentStr;
          step.currentContext = 'Success';
        }
      } else {
        step.actionResult = 'Tool executed';
        step.currentContext = 'Success';
      }
      steps.push(step);
    }
  }

  return steps;
}

/**
 * Create empty execution step
 */
function createEmptyStep(): Omit<ExecutionStep, 'stepNumber' | 'timestamp'> {
  return {
    agentAction: '',
    actionResult: '',
    currentContext: '',
  };
}
